version: "3.7"

services:
  n8n:
    build: .
    image: petpos-n8n:latest
    container_name: n8n
    restart: unless-stopped
    ports:
      - "5680:5680"
    environment:
      # Core
      - N8N_HOST=localhost
      - N8N_PORT=5680
      - N8N_PROTOCOL=http
      - GENERIC_TIMEZONE=Asia/Colombo
      - N8N_RUNNERS_ENABLED=true
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_BLOCK_ENV_ACCESS_IN_NODE=false

      # DB (external Postgres on host)
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=host.docker.internal
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=${DB_POSTGRESDB_DATABASE}
      - DB_POSTGRESDB_SCHEMA=public
      - DB_POSTGRESDB_USER=${DB_USER}
      - DB_POSTGRESDB_PASSWORD=${DB_PASSWORD}
      - DB_LOGGING_ENABLED=true

      # Executions & logging
      - EXECUTIONS_DATA_SAVE_ON_ERROR=all
      - EXECUTIONS_DATA_SAVE_ON_SUCCESS=all
      - EXECUTIONS_DATA_SAVE_ON_PROGRESS=false

      # Code node libs
      - NODE_FUNCTION_ALLOW_BUILTIN=*
      - NODE_FUNCTION_ALLOW_EXTERNAL=moment,lodash,stytch,resend,nodemailer,jwt-decode,axios,jsonwebtoken,uuid

    volumes:
      - n8n_data_postgres:/home/<USER>/.n8n

volumes:
  n8n_data_postgres:
    external: true
